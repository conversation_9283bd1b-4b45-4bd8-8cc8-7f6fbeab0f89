# RiderService SDK API重新设计方案

**版本**: 2.0 | **状态**: API设计 | **类型**: 架构重构

---

## 目录

- [设计背景](#设计背景)
- [当前API问题分析](#当前api问题分析)
- [新API架构设计](#新api架构设计)
- [API接口详细设计](#api接口详细设计)
- [使用示例](#使用示例)
- [迁移指南](#迁移指南)
- [实施计划](#实施计划)

---

## 设计背景

### 当前API存在的核心问题

RiderService当前提供了**25个公开API方法**，存在以下严重问题：

| 问题类型 | 具体表现 | 影响程度 |
|---------|---------|---------|
| **命名不一致** | `startBleScan()` vs `sendNaviModeChange()` | 高 |
| **参数风格混乱** | 有些用对象，有些用基本类型 | 中 |
| **返回值不统一** | 有些返回值，有些void，有些抛异常 | 高 |
| **异步处理不一致** | 有些同步，有些异步，有些回调 | 高 |
| **职责混乱** | 单一方法处理多种消息类型 | 高 |

### 设计原则违反

1. **违反单一职责原则**: `sendMessageToRiderService()` 处理12种不同类型的消息
2. **违反接口隔离原则**: `RiderServiceCallback` 包含25个回调方法
3. **违反最少知识原则**: 暴露了内部实现细节
4. **违反一致性原则**: API命名和行为不一致

---

## 当前API问题分析

### 1. 消息发送API设计问题

**当前实现问题:**
```kotlin
// 巨型switch-case，处理12种消息类型，逻辑不一致
fun sendMessageToRiderService(message: RiderMessage) {
    when (message) {
        is NotificationInfo -> handleNotificationInfo(message)
        is WeatherInfo -> handleWeatherInfo(message)
        is NaviInfo -> handleNaviInfo(message)
        is ArriveDestination -> handleArriveDestination()
        is NaviStop -> {
            val id = RiderProtocol.NaviMessageId.MSG_NAVI_STOP_VALUE
            val protocolMessage = RiderProtocol.NaviStop.newBuilder().build()
            sendMessageToRiderService(id, protocolMessage)
        }
        is NaviStart -> { /* 处理导航开始 */ }
        is GpsSignal -> { /* 处理GPS信号 */ }
        is NaviCross -> { /* 处理路口信息 */ }
        is LaneInfo -> { /* 处理车道信息 */ }
        is NaviRoute -> { /* 处理路线信息 */ }
        is NaviText -> { /* 处理导航文本 */ }
        is AutoLinkConnect -> { /* 处理AutoLink连接 */ }
        // 危险：未知消息类型被忽略
    }
}
```

**问题分析:**
- **认知负担重**: 用户需要了解12种消息类型
- **类型安全性差**: 运行时才能发现类型错误
- **扩展性差**: 新增消息类型需要修改核心方法
- **测试困难**: 需要测试所有消息类型的分支

### 2. 导航控制API混乱

**职责重叠的API:**
```kotlin
fun setNaviMode(naviMode: NaviMode)              // 设置导航模式
fun sendNaviModeChange(naviMode: NaviMode)       // 发送导航模式变更
fun sendNaviModeStart(naviMode: NaviMode)        // 开始导航模式
fun sendNaviModeStop(naviMode: NaviMode)         // 停止导航模式
fun needChangeDisplay(): Boolean                 // 暴露内部状态
```

**实际问题:**
- **职责重叠**: 多个方法做类似的事情
- **状态管理混乱**: shouldChangeDisplay逻辑复杂且难以理解
- **封装性破坏**: needChangeDisplay暴露了内部实现细节

### 3. 配置管理API安全问题

**暴露内部实现:**
```kotlin
// 直接暴露内部ConfigPreferences对象，用户可以进行危险操作
fun getConfigPreferences(): ConfigPreferences {
    return configPreferences ?: throw IllegalStateException("Please call the RiderService#init() first")
}

// 危险示例：用户可以直接清除所有配置
val config = riderService.getConfigPreferences()
config.clearAllConfig()  // 可能导致系统异常
```

**问题分析:**
- **封装性破坏**: 直接暴露内部配置对象
- **安全风险**: 用户可以直接修改配置
- **API碎片化**: 每个配置项都有单独的getter

### 4. 回调接口过大问题

**当前回调方法分类:**
```kotlin
abstract class RiderServiceCallback {
    // 连接相关 (8个方法)
    open fun onScanResult(bleDevices: List<BleDevice>) {}
    open fun onScanning() {}
    open fun onScanFinish() {}
    open fun onConnectStatusChange(status: Connection) {}
    open fun onNeedBluetoothScanPermission() {}
    open fun onRequestOpenBluetooth() {}
    open fun onNeedLocationPermission() {}
    open fun onWifiState(isWifiOpened: Boolean) {}

    // 投屏相关 (6个方法)
    open fun onDisplayInitialized(display: Display) {}
    open fun onDisplayReleased(display: Display) {}
    open fun onVideoChannelReady() {}
    open fun onRequestMediaProjection() {}
    open fun onMirrorStart() {}
    open fun onMirrorStop() {}

    // 导航消息相关 (7个方法)
    open fun onNaviModeChange(mode: NaviMode) {}
    open fun onNaviModeChangeResponse(mode: NaviMode, isReady: Boolean) {}
    open fun onNaviModeStopResponse(mode: NaviMode) {}
    open fun onNaviModeStartResponse(mode: NaviMode) {}
    open fun onRequestWeatherInfo() {}
    open fun onConfigChange(config: RiderServiceConfig) {}
    open fun changeMap(mapType: Int) {}

    // 系统相关 (4个方法)
    open fun onClusterReady() {}
    open fun onDialogShow(title: String, message: String) {}
    open fun naviVersionResponse(version: String) {}
    open fun onEnableNotificationFailed(device: BluetoothDevice, status: Int) {}
}
```

**问题分析:**
- **方法过多**: 包含25个回调方法
- **职责混乱**: 连接、投屏、消息、权限等混在一起
- **违反ISP**: 客户端被迫依赖不需要的方法

---

## 新API架构设计

### suspend方法使用原则

**需要suspend的操作（需要等待结果）:**
- `startBleScan()` - 扫描设备并等待结果
- `connect()` - 连接设备并等待连接建立
- `disconnect()` - 断开连接并等待完成
- `startNavigation()` - 启动导航模式并等待确认
- `stopNavigation()` - 停止导航模式并等待确认

**不需要suspend的操作（立即返回）:**
- 所有消息发送操作 - 只是将消息放入队列
- 配置获取操作 - 同步读取本地配置
- 状态查询操作 - 同步返回当前状态
- 监听器注册/注销 - 同步操作

### 整体架构图

```mermaid
graph TB
    subgraph Current["当前API结构"]
        CurrentAPI[RiderService<br/>25个混合方法]
        CurrentCallback[RiderServiceCallback<br/>25个回调方法]
    end

    subgraph New["新API架构"]
        RiderService[RiderService<br/>主入口]

        subgraph Managers["功能管理器"]
            ConnectionMgr[ConnectionManager<br/>连接管理]
            MessagingMgr[MessagingManager<br/>消息通信]
            NavigationMgr[NavigationManager<br/>导航控制]
            ProjectionMgr[ProjectionManager<br/>投屏控制]
            ConfigMgr[ConfigManager<br/>配置管理]
        end

        subgraph Listeners["分组监听器"]
            ConnListener[ConnectionListener<br/>8个连接相关回调]
            NaviListener[NavigationListener<br/>7个导航相关回调]
            ProjListener[ProjectionListener<br/>6个投屏相关回调]
            MsgListener[MessageListener<br/>4个消息相关回调]
        end
    end

    Current --> New
    RiderService --> ConnectionMgr
    RiderService --> MessagingMgr
    RiderService --> NavigationMgr
    RiderService --> ProjectionMgr
    RiderService --> ConfigMgr

    ConnectionMgr -.-> ConnListener
    NavigationMgr -.-> NaviListener
    ProjectionMgr -.-> ProjListener
    MessagingMgr -.-> MsgListener

    classDef current fill:#d32f2f,stroke:#f44336,stroke-width:2px,color:#ffffff
    classDef manager fill:#1565c0,stroke:#42a5f5,stroke-width:2px,color:#ffffff
    classDef listener fill:#4a148c,stroke:#ab47bc,stroke-width:2px,color:#ffffff
    classDef main fill:#1b5e20,stroke:#66bb6a,stroke-width:3px,color:#ffffff

    class CurrentAPI,CurrentCallback current
    class ConnectionMgr,MessagingMgr,NavigationMgr,ProjectionMgr,ConfigMgr manager
    class ConnListener,NaviListener,ProjListener,MsgListener listener
    class RiderService main
```

### 设计原则

1. **按功能域分组**: 将API按照连接、消息、导航、投屏、配置等功能域分组
2. **统一异步处理**: 所有异步操作使用Kotlin协程和Result类型
3. **类型安全**: 使用强类型和密封类确保类型安全
4. **接口隔离**: 按职责拆分回调接口，客户端只需实现需要的接口
5. **封装性**: 隐藏内部实现细节，提供安全的配置访问
6. **一致性**: 统一的命名规范、参数风格和返回值处理

### 核心改进点

| 改进项 | 当前问题 | 新设计方案 | 收益 |
|-------|---------|-----------|------|
| **API分组** | 25个方法混在一个类中 | 按功能域分为5个管理器 | 职责清晰，易于理解 |
| **消息发送** | 单一方法处理14种消息 | 按消息类型分为专门方法 | 类型安全，易于扩展 |
| **异步处理** | 回调、同步、异步混乱 | 统一使用协程+Result | 一致的异步体验 |
| **回调接口** | 25个方法的巨型接口 | 按功能拆分为4个接口 | 接口隔离，按需实现 |
| **配置管理** | 直接暴露内部对象 | 安全的配置访问API | 封装性，安全性 |
| **错误处理** | 异常、返回值混乱 | 统一的错误类型系统 | 一致的错误处理 |

---

## 📚 API接口详细设计

### 1. RiderService - 主入口API

```kotlin
interface RiderService {
    // === 管理器访问 ===
    val connection: ConnectionManager
    val messaging: MessagingManager
    val navigation: NavigationManager
    val projection: ProjectionManager
    val config: ConfigManager

    // === 生命周期管理 ===
    fun init(application: Application): Result<Unit>
    fun destroy()
    fun isInitialized(): Boolean

    // === 全局回调管理 ===
    fun addGlobalCallback(callback: RiderServiceCallback)
    fun removeGlobalCallback(callback: RiderServiceCallback)
    fun clearAllCallbacks()

    // === 版本信息 ===
    fun getSdkVersion(): String
    fun getApiVersion(): String

    companion object {
        fun getInstance(): RiderService
    }
}
```

### 2. ConnectionManager - 连接管理API

```kotlin
interface ConnectionManager {
    // === 设备扫描 ===
    suspend fun startBleScan(timeoutMs: Long = 10000): Result<List<BleDevice>>
    fun stopBleScan()
    fun isScanning(): Boolean

    // === 设备连接 ===
    suspend fun connect(device: BleDevice): Result<Unit>
    suspend fun disconnect(): Result<Unit>
    fun isConnected(): Boolean
    fun getCurrentDevice(): BleDevice?

    // === 连接状态 ===
    fun getConnectionStatus(): Connection

    // === 事件监听 ===
    fun addConnectionListener(listener: ConnectionListener)
    fun removeConnectionListener(listener: ConnectionListener)
    fun clearAllListeners()
}

// 连接事件监听器（基于现有回调方法）
interface ConnectionListener {
    fun onScanResult(bleDevices: List<BleDevice>) {}
    fun onScanning() {}
    fun onScanFinish() {}
    fun onConnectStatusChange(status: Connection) {}
    fun onNeedBluetoothScanPermission() {}
    fun onRequestOpenBluetooth() {}
    fun onNeedLocationPermission() {}
    fun onWifiState(isWifiOpened: Boolean) {}
}
```

### 3. MessagingManager - 消息通信API

```kotlin
interface MessagingManager {
    // === 导航消息（基于现有NaviInfo等结构）===
    fun sendNaviInfo(naviInfo: NaviInfo): Boolean
    fun sendNaviRoute(naviRoute: NaviRoute): Boolean
    fun sendLaneInfo(laneInfo: LaneInfo): Boolean
    fun sendNaviCross(naviCross: NaviCross): Boolean
    fun sendNaviText(naviText: NaviText): Boolean
    fun sendArriveDestination(): Boolean

    // === 系统消息（基于现有结构）===
    fun sendNotificationInfo(notificationInfo: NotificationInfo): Boolean
    fun sendWeatherInfo(weatherInfo: WeatherInfo): Boolean
    fun sendGpsSignal(gpsSignal: GpsSignal): Boolean

    // === 控制消息 ===
    fun sendNaviStart(): Boolean
    fun sendNaviStop(): Boolean
    fun sendAutoLinkConnect(autoLinkConnect: AutoLinkConnect): Boolean

    // === 消息状态 ===
    fun getMessageQueueSize(): Int
    fun clearMessageQueue()

    // === 事件监听 ===
    fun addMessageListener(listener: MessageListener)
    fun removeMessageListener(listener: MessageListener)
}

// 消息事件监听器
interface MessageListener {
    fun onMessageSent(messageType: String, success: Boolean) {}
    fun onMessageError(error: String) {}
    fun onQueueSizeChanged(size: Int) {}
}


```

### 4. NavigationManager - 导航控制API

```kotlin
interface NavigationManager {
    // === 导航模式控制（基于现有NaviMode）===
    suspend fun startNavigation(naviMode: NaviMode): Result<Unit>
    suspend fun stopNavigation(): Result<Unit>
    fun setNaviMode(naviMode: NaviMode)
    fun sendNaviModeChange(naviMode: NaviMode)
    fun sendNaviModeStart(naviMode: NaviMode)
    fun sendNaviModeStop(naviMode: NaviMode)

    // === 导航状态查询 ===
    fun getCurrentNaviMode(): NaviMode?
    fun needChangeDisplay(): Boolean

    // === 导航响应处理 ===
    fun sendNaviModeChangeResponse(result: NaviModeChangeResult)

    // === 事件监听 ===
    fun addNavigationListener(listener: NavigationListener)
    fun removeNavigationListener(listener: NavigationListener)
}

// 导航事件监听器（基于现有回调方法）
interface NavigationListener {
    fun onNaviModeChange(naviMode: NaviMode) {}
    fun onNaviModeChangeResponse(naviMode: NaviMode, isReady: Boolean) {}
    fun onNaviModeStopResponse(naviMode: NaviMode) {}
    fun onNaviModeStartResponse(naviMode: NaviMode) {}
    fun onRequestWeatherInfo() {}
    fun onConfigChange(config: RiderServiceConfig) {}
    fun changeMap(mapType: Int) {}
}
```

### 5. ProjectionManager - 投屏控制API

```kotlin
interface ProjectionManager {
    // === MediaProjection管理 ===
    fun setMediaProjection(mediaProjection: MediaProjection)
    fun getMediaProjection(): MediaProjection?

    // === 锁屏投屏 ===
    suspend fun initLockScreenProjection(
        context: Context,
        mediaProjection: MediaProjection,
        displayMetrics: DisplayMetrics
    ): Result<Unit>
    suspend fun requestLockScreenDisplay(): Result<Display>

    // === 事件监听 ===
    fun addProjectionListener(listener: ProjectionListener)
    fun removeProjectionListener(listener: ProjectionListener)
}

// 投屏事件监听器（基于现有回调方法）
interface ProjectionListener {
    fun onDisplayInitialized(display: Display) {}
    fun onDisplayReleased(display: Display) {}
    fun onVideoChannelReady() {}
    fun onRequestMediaProjection() {}
    fun onMirrorStart() {}
    fun onMirrorStop() {}
}
```

### 6. ConfigManager - 配置管理API

```kotlin
interface ConfigManager {
    // === 只读配置信息（基于现有方法）===
    fun getProductKey(): String
    fun getUuid(): String
    fun getAutolinkVersion(): String
    fun getApplication(): Application?

    // === 连接配置 ===
    fun getWifiConnectionMode(): WifiConnectionMode
    fun setWifiConnectionMode(mode: WifiConnectionMode)

    // === 配置管理（安全访问，不直接暴露ConfigPreferences）===
    fun deleteConfig()

    // === 配置监听 ===
    fun addConfigListener(listener: ConfigListener)
    fun removeConfigListener(listener: ConfigListener)
}

// 配置事件监听器
interface ConfigListener {
    fun onConfigChanged(config: RiderServiceConfig) {}
    fun onDialogShow(title: String, message: String) {}
}
```

---

## 使用示例

### 基本使用流程

```kotlin
class MainActivity : AppCompatActivity() {
    private lateinit var riderService: RiderService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 获取RiderService实例
        riderService = RiderService.getInstance()

        // 初始化SDK
        riderService.init(application)
        Log.d("RiderService", "SDK初始化成功")
        setupRiderService()
    }

    private fun setupRiderService() {
        // 设置连接监听
        riderService.connection.addConnectionListener(object : ConnectionListener {
            override fun onConnectStatusChange(status: Connection) {
                when (status) {
                    Connection.CONNECTED -> {
                        Log.d("Connection", "设备已连接")
                        startNavigation()
                    }
                    Connection.DISCONNECTED -> {
                        Log.d("Connection", "设备已断开")
                    }
                    else -> { /* 其他状态处理 */ }
                }
            }
        })

        // 开始扫描设备
        scanAndConnect()
    }

    private suspend fun scanAndConnect() {
        try {
            // 扫描设备
            val devices = riderService.connection.startBleScan(15000).getOrThrow()

            if (devices.isNotEmpty()) {
                // 连接第一个设备
                riderService.connection.connect(devices.first()).getOrThrow()
                Log.d("Connection", "连接成功: ${devices.first().name}")
            } else {
                Log.w("Connection", "未发现设备")
            }
        } catch (e: Exception) {
            Log.e("Connection", "扫描或连接失败", e)
        }
    }

    private suspend fun startNavigation() {
        try {
            // 开始导航
            riderService.navigation.startNavigation(NaviMode.ScreenNavi).getOrThrow()

            // 发送导航信息（使用现有NaviInfo结构）
            val success = riderService.messaging.sendNaviInfo(
                NaviInfo(
                    curLink = 1,
                    curPoint = 1,
                    curStep = 1,
                    curStepRetainDistance = 1200,
                    curStepRetainTime = 180,
                    naviType = 1,
                    iconType = 2,
                    pathRetainTime = 1800,
                    pathRetainDistance = 5000,
                    routeRemainLightCount = 3,
                    pathId = 12345L,
                    nextRoadName = "科技园路",
                    currentRoadName = "当前路",
                    mapType = 1,
                    turnIconName = "turn_left",
                    turnKind = "left"
                )
            )

            if (success) {
                Log.d("Navigation", "导航信息发送成功")
            }
        } catch (e: Exception) {
            Log.e("Navigation", "导航启动失败", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        riderService.destroy()
    }
}
```

### 消息发送示例

```kotlin
// 发送通知信息（使用现有NotificationInfo结构）
val success = riderService.messaging.sendNotificationInfo(
    NotificationInfo(
        appName = "微信",
        title = "新消息",
        content = "您有一条新消息"
    )
)

// 发送天气信息（使用现有WeatherInfo结构）
riderService.messaging.sendWeatherInfo(
    WeatherInfo(
        wea = "晴",
        tem = "25",
        humidity = "60",
        pressure = "1013",
        altitude = "100"
    )
)

// 发送车道信息（使用现有LaneInfo结构）
riderService.messaging.sendLaneInfo(
    LaneInfo(
        backgroundLane = intArrayOf(1, 2, 3),
        frontLane = intArrayOf(1, 0, 1),
        laneCount = 3
    )
)

// 发送GPS信号状态
riderService.messaging.sendGpsSignal(
    GpsSignal(isWeek = false)
)

// 发送导航文本
riderService.messaging.sendNaviText(
    NaviText(
        type = 1,
        text = "前方500米左转"
    )
)
```

### 投屏控制示例

```kotlin
// 设置MediaProjection
val mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
val intent = mediaProjectionManager.createScreenCaptureIntent()
// 在onActivityResult中获取MediaProjection
val mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, data)
riderService.projection.setMediaProjection(mediaProjection)

// 开始屏幕投屏
riderService.projection.startScreenProjection().onSuccess {
    Log.d("Projection", "屏幕投屏启动成功")
}.onFailure { error ->
    when (error) {
        is ProjectionError.MediaProjectionNotSet -> {
            Log.e("Projection", "请先设置MediaProjection")
        }
        is ProjectionError.PermissionDenied -> {
            Log.e("Projection", "投屏权限被拒绝")
        }
        else -> {
            Log.e("Projection", "投屏启动失败: ${error.message}")
        }
    }
}
```

---

## 迁移指南

### 从旧API迁移到新API

| 旧API | 新API | 迁移说明 |
|-------|-------|---------|
| `riderService.startBleScan()` | `riderService.connection.startBleScan()` | 移动到ConnectionManager，返回Result |
| `riderService.connect(device)` | `riderService.connection.connect(device)` | 移动到ConnectionManager，返回Result |
| `riderService.sendMessageToRiderService(NaviInfo(...))` | `riderService.messaging.sendNaviInfo(...)` | 拆分为具体的消息发送方法 |
| `riderService.sendMessageToRiderService(NotificationInfo(...))` | `riderService.messaging.sendNotificationInfo(...)` | 拆分为具体的消息发送方法 |
| `riderService.setNaviMode(mode)` | `riderService.navigation.setNaviMode(mode)` | 移动到NavigationManager |
| `riderService.setMediaProjection(projection)` | `riderService.projection.setMediaProjection(projection)` | 移动到ProjectionManager |
| `riderService.getConfigPreferences()` | 使用ConfigManager的安全方法 | 不再直接暴露ConfigPreferences |

### 回调接口迁移

```kotlin
// 旧方式：实现巨型回调接口
class MyCallback : RiderServiceCallback() {
    override fun onScanResult(devices: List<BleDevice>) { /* ... */ }
    override fun onDisplayInitialized(display: Display) { /* ... */ }
    override fun onNaviModeChange(mode: NaviMode) { /* ... */ }
    // 需要实现25个方法...
}

// 新方式：按需实现专门的回调接口
class MyConnectionListener : ConnectionListener {
    override fun onScanResult(devices: List<BleDevice>) { /* ... */ }
    override fun onConnectionStateChanged(status: ConnectionStatus) { /* ... */ }
}

class MyProjectionListener : ProjectionListener {
    override fun onDisplayInitialized(display: Display) { /* ... */ }
    override fun onProjectionStarted(info: ProjectionInfo) { /* ... */ }
}

class MyNavigationListener : NavigationListener {
    override fun onNavigationModeChanged(mode: NavigationMode) { /* ... */ }
    override fun onNavigationStatusChanged(status: NavigationStatus) { /* ... */ }
}

// 注册监听器
riderService.connection.addConnectionListener(MyConnectionListener())
riderService.projection.addProjectionListener(MyProjectionListener())
riderService.navigation.addNavigationListener(MyNavigationListener())
```

### 消息发送迁移

```kotlin
// 旧方式：使用统一的消息发送方法
riderService.sendMessageToRiderService(NaviInfo(...))
riderService.sendMessageToRiderService(NotificationInfo(...))
riderService.sendMessageToRiderService(WeatherInfo(...))

// 新方式：使用类型安全的专门方法（保持现有数据结构）
riderService.messaging.sendNaviInfo(NaviInfo(...))
riderService.messaging.sendNotificationInfo(NotificationInfo(...))
riderService.messaging.sendWeatherInfo(WeatherInfo(...))
```

### 异步处理迁移

```kotlin
// 旧方式：回调方式
riderService.startBleScan()
// 在回调中处理结果
override fun onScanResult(devices: List<BleDevice>) {
    // 处理扫描结果
}

// 新方式：协程+Result（仅对需要等待结果的操作）
lifecycleScope.launch {
    riderService.connection.startBleScan().onSuccess { devices ->
        // 处理扫描结果
    }.onFailure { error ->
        // 处理错误
    }
}

// 消息发送仍然是同步的（立即返回Boolean）
val success = riderService.messaging.sendNaviInfo(naviInfo)
if (success) {
    Log.d("Messaging", "消息发送成功")
}
```

---

## 实施计划

### Phase 1: API接口设计 (1周)

**目标**: 完成新API接口设计
- 设计ConnectionManager、MessagingManager等接口
- 定义新的回调接口分组（基于现有回调方法）
- 保持现有数据结构不变
- 编写API使用示例和文档

### Phase 2: 消息API重构 (1周)

**目标**: 拆分sendMessageToRiderService方法
- 将sendMessageToRiderService拆分为12个专门方法
- 保持现有消息类型和结构不变
- 实现类型安全的消息发送
- 保持协议兼容性

### Phase 3: 回调接口重构 (1周)

**目标**: 按功能拆分回调接口
- 将25个回调方法按功能分为4个接口
- 实现向后兼容的适配器
- 迁移现有回调使用

### Phase 4: 连接和导航API重构 (1周)

**目标**: 重构连接和导航管理
- 实现ConnectionManager和NavigationManager
- 合理使用suspend方法（仅对需要等待结果的操作）
- 保持现有NaviMode等枚举不变

### Phase 5: 测试和文档 (1周)

**目标**: 完善测试和文档
- 完善单元测试和集成测试
- 更新API文档和使用指南
- 编写迁移指南

### 总体时间安排

| 阶段 | 内容 | 工作量 | 关键里程碑 |
|-----|------|--------|-----------|
| **Phase 1** | API接口设计 | 1周 | 新API设计完成 |
| **Phase 2** | 消息API重构 | 1周 | 消息发送API重构完成 |
| **Phase 3** | 回调接口重构 | 1周 | 回调接口拆分完成 |
| **Phase 4** | 连接和导航API重构 | 1周 | 核心功能API完成 |
| **Phase 5** | 测试和文档 | 1周 | 文档和测试完善 |
| **总计** | | **5周** | **SDK 2.0 API发布** |

### 关键原则

1. **保持现有数据结构**: 不修改NaviInfo、NotificationInfo等现有消息类型
2. **合理使用suspend**: 只对真正需要等待结果的操作使用suspend
3. **向后兼容**: 保持现有API可用，提供迁移期
4. **渐进重构**: 分阶段实施，降低风险

### 成功标准

1. **职责清晰**: 每个管理器职责单一明确
2. **接口隔离**: 回调接口按功能分组，客户端按需实现
3. **类型安全**: 消息发送方法类型安全，编译时发现错误
4. **易用性**: 开发者能够快速理解和使用新API
5. **兼容性**: 现有数据结构和协议保持不变
